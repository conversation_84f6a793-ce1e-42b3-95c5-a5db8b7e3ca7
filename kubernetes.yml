---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: windows-pvc
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 64Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: windows
  labels:
    name: windows
spec:
  replicas: 1
  selector:
    matchLabels:
      app: windows
  template:
    metadata:
      labels:
        app: windows
    spec:
      containers:
      - name: windows
        image: dockurr/windows
        env:
        - name: VERSION
          value: "11"
        - name: DISK_SIZE
          value: "64G"
        ports:
          - containerPort: 8006
            name: http
            protocol: TCP
          - containerPort: 3389
            name: rdp
            protocol: TCP
          - containerPort: 3389
            name: udp
            protocol: UDP
          - containerPort: 5900
            name: vnc
            protocol: TCP
        securityContext:
          capabilities:
            add:
            - NET_ADMIN
          privileged: true
        volumeMounts:
        - mountPath: /storage
          name: storage
        - mountPath: /dev/kvm
          name: dev-kvm
        - mountPath: /dev/net/tun
          name: dev-tun
      terminationGracePeriodSeconds: 120
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: windows-pvc
      - hostPath:
          path: /dev/kvm
        name: dev-kvm
      - hostPath:
          path: /dev/net/tun
          type: CharDevice
        name: dev-tun
---
apiVersion: v1
kind: Service
metadata:
  name: windows
spec:
  internalTrafficPolicy: Cluster
  ports:
    - name: http
      port: 8006
      protocol: TCP
      targetPort: 8006
    - name: rdp
      port: 3389
      protocol: TCP
      targetPort: 3389
    - name: udp
      port: 3389
      protocol: UDP
      targetPort: 3389
    - name: vnc
      port: 5900
      protocol: TCP
      targetPort: 5900
  selector:
    app: windows
  type: ClusterIP
